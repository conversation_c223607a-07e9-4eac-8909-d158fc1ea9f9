import React from "react";
import { Badge, Tooltip } from "antd";
import { JobResp } from "@/app/store/modules/candidate";
import { TextView } from "@/app/components/TextView";
import styles from "./CompactJobCard.module.scss";

interface CompactJobCardProps {
    job: JobResp;
    onClick: () => void;
    className?: string;
}

const CompactJobCard: React.FC<CompactJobCardProps> = ({ job, onClick, className }) => {
    const candidateCount = job?.count ?? 0;
    const jobName = job?.formatStr || job?.jobNameOuter || "职位";
    const orgName = job?.orgName || "";

    return (
        <Tooltip
            title={
                <div>
                    <div style={{ fontWeight: 600, marginBottom: 4 }}>{jobName}</div>
                    <div style={{ fontSize: 12, opacity: 0.8 }}>{orgName}</div>
                    <div style={{ fontSize: 12, marginTop: 4 }}>
                        {candidateCount > 0 ? `${candidateCount} 位候选人` : "暂无候选人"}
                    </div>
                    <div style={{ fontSize: 11, marginTop: 2, opacity: 0.8 }}>点击展开完整列表</div>
                </div>
            }
            placement="right"
        >
            <div className={`${styles["compact-job-card"]} ${className || ""}`} onClick={onClick}>
                {/* <Badge count={candidateCount} className={styles["badge-wrapper"]} size="small" /> */}
                <div className={styles["card-content"]}>
                    {/* <div className={styles["job-icon"]}>
                        <span className={styles["job-initial"]}>{jobName.charAt(0)}</span>
                    </div> */}
                    <div className={styles["job-info"]}>
                        <div className={styles["job-name"]}>
                            <TextView text={jobName} lines={2} />
                        </div>
                        <div className={styles["candidate-count"]}>{candidateCount}</div>
                    </div>
                </div>
            </div>
        </Tooltip>
    );
};

export default CompactJobCard;
