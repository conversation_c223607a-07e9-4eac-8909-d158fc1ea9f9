.compact-job-card {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 12px;
    background: var(--white);
    border: 2px solid var(--primary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 8px auto;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-hover, #40a9ff);
    }

    .badge-wrapper {
        position: absolute;
        top: -8px;
        right: -8px;
        z-index: 10;

        :global {
            .ant-badge-count {
                background-color: var(--primary);
                border: 2px solid var(--white);
                font-size: 10px;
                min-width: 18px;
                height: 18px;
                line-height: 14px;
                padding: 0 4px;
            }
        }
    }

    .card-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        text-align: center;
    }

    .job-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary), var(--primary-hover, #40a9ff));
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 4px;

        .job-initial {
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
    }

    .job-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
    }

    .job-name {
        font-size: 10px;
        color: var(--main-text-color);
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: 2px;
        width: 100%;
        text-align: center;
        
        :global {
            .text-view {
                font-size: 10px !important;
            }
        }
    }

    .candidate-count {
        font-size: 12px;
        color: var(--primary);
        font-weight: 600;
    }
}

// Animation for the compact card appearance
@keyframes compactCardSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateX(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateX(0);
    }
}

.compact-job-card {
    animation: compactCardSlideIn 0.3s ease-out;
}

// Responsive adjustments
@media (max-width: 768px) {
    .compact-job-card {
        width: 70px;
        height: 70px;

        .job-icon {
            width: 28px;
            height: 28px;

            .job-initial {
                font-size: 12px;
            }
        }

        .job-name {
            font-size: 9px;
        }

        .candidate-count {
            font-size: 10px;
        }
    }
}

// Hover effects for better interactivity
.compact-job-card:hover {
    .job-icon {
        transform: scale(1.1);
        transition: transform 0.2s ease;
    }

    .candidate-count {
        color: var(--primary-hover, #40a9ff);
    }
}

// Focus styles for accessibility
.compact-job-card:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}
